package com.yy.hd.qly.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-08-04 10:23
 **/
@Configuration
public class KafkaConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Bean
    @ConfigurationProperties("kafka.data-report")
    public KafkaProperties dataReportKafkaProperties() {
        return new KafkaProperties();
    }

    @Bean
    public KafkaListenerContainerFactory dataReportKafkaContainerFactory(KafkaProperties dataReportKafkaProperties) {
        return createContainerFactory(dataReportKafkaProperties);
    }

    public static ConcurrentKafkaListenerContainerFactory createContainerFactory(KafkaProperties kafkaProperties) {
        ConcurrentKafkaListenerContainerFactory containerFactory = new ConcurrentKafkaListenerContainerFactory();
//        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa()) {
//            log.warn("当前环境默认不启动Consumer.");
//            containerFactory.setAutoStartup(false);
//        }
        //设置过大容易导致负载压在某台机器上
        containerFactory.setConcurrency(2);
        //消费失败不自动提交
        kafkaProperties.getConsumer().setEnableAutoCommit(false);
        //消费失败重试处理
//        containerFactory.setErrorHandler(kafkaErrorHandler());
        //消费者工厂
        containerFactory.setConsumerFactory(new DefaultKafkaConsumerFactory(kafkaProperties.buildConsumerProperties()));
        //日志追踪
//        containerFactory.setRecordInterceptor(record -> {
//            String topic = record.topic();
//            MDCUtils.putContext("kafka=" + topic);
//            return record;
//        });
        return containerFactory;
    }
}
