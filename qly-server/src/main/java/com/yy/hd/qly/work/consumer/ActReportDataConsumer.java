package com.yy.hd.qly.work.consumer;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.yy.hd.qly.client.model.ActReportData;
import com.yy.hd.qly.service.ActReportDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 活动上报数据Kafka消费者
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class ActReportDataConsumer {

    public static final String TOPIC = "qly_report_topic";

    @Autowired
    private ActReportDataService actReportDataService;

    private final Gson gson = new Gson();

    /**
     * 消费活动上报数据消息
     *
     * @param records Kafka消息记录列表
     */
    @KafkaListener(topics = TOPIC, containerFactory = "dataReportKafkaContainerFactory")
    public void consumeActReportData(List<ConsumerRecord<String, String>> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        log.info("开始消费活动上报数据，消息数量: {}", records.size());

        List<ActReportData> actReportDataList = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (ConsumerRecord<String, String> record : records) {
            try {
                String message = record.value();
                if (Objects.isNull(message) || message.trim().isEmpty()) {
                    log.warn("消息内容为空，跳过处理");
                    failCount++;
                    continue;
                }

                // 解析JSON消息为ActReportData对象
                ActReportData actReportData = gson.fromJson(message, ActReportData.class);
                if (actReportData == null) {
                    log.warn("解析消息失败，消息内容: {}", message);
                    failCount++;
                    continue;
                }

                actReportDataList.add(actReportData);
                successCount++;

                log.debug("成功解析消息，traceId: {}, product: {}, service: {}",
                         actReportData.getTraceId(), actReportData.getProduct(), actReportData.getService());

            } catch (JsonSyntaxException e) {
                log.error("JSON解析失败，消息内容: {}", record.value(), e);
                failCount++;
            } catch (Exception e) {
                log.error("处理消息异常，消息内容: {}", record.value(), e);
                failCount++;
            }
        }

        // 批量保存到数据库
        if (!actReportDataList.isEmpty()) {
            boolean saveResult = actReportDataService.batchSaveActReportData(actReportDataList);
            if (saveResult) {
                log.info("批量保存活动上报数据成功，成功解析: {}, 解析失败: {}, 保存条数: {}",
                        successCount, failCount, actReportDataList.size());
            } else {
                log.error("批量保存活动上报数据失败，成功解析: {}, 解析失败: {}, 尝试保存条数: {}",
                         successCount, failCount, actReportDataList.size());
            }
        } else {
            log.warn("没有有效的活动上报数据需要保存，成功解析: {}, 解析失败: {}", successCount, failCount);
        }
    }
}
