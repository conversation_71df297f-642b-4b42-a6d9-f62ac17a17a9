package com.yy.hd.qly.controller;

import com.yy.hd.qly.client.model.ActReportData;
import com.yy.hd.qly.service.ActReportDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 活动上报数据控制器（用于测试）
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/act-report")
public class ActReportDataController {

    @Autowired
    private ActReportDataService actReportDataService;

    /**
     * 测试保存活动上报数据
     *
     * @param actReportData 活动上报数据
     * @return 保存结果
     */
    @PostMapping("/save")
    public String saveActReportData(@RequestBody ActReportData actReportData) {
        try {
            boolean result = actReportDataService.saveActReportData(actReportData);
            if (result) {
                return "保存成功";
            } else {
                return "保存失败";
            }
        } catch (Exception e) {
            log.error("保存活动上报数据异常", e);
            return "保存异常: " + e.getMessage();
        }
    }

    /**
     * 创建测试数据
     *
     * @return 测试数据
     */
    @GetMapping("/test-data")
    public ActReportData createTestData() {
        return ActReportData.builder()
                .batchTraceId("batch_" + System.currentTimeMillis())
                .traceId("trace_" + System.currentTimeMillis())
                .traceBeginTime(System.currentTimeMillis())
                .product("hdpt")
                .service("qly-server")
                .scene("测试场景")
                .method("testMethod")
                .dataType(2)
                .key1("test_key1")
                .key2("test_key2")
                .key3("test_key3")
                .content("这是一条测试数据")
                .extData("{\"test\": \"data\"}")
                .timestamp(System.currentTimeMillis())
                .actId(12345L)
                .cmptId(67890L)
                .cmptIndex(1)
                .rankId(0L)
                .phaseId(0L)
                .timeCode(0L)
                .uid(1001L)
                .sid(2001L)
                .ssid(3001L)
                .build();
    }
}
