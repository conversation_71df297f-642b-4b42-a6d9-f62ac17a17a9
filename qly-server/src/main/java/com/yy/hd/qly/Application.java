package com.yy.hd.qly;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.yy.boot.component.kafka.MultiKafkaAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@EnableApolloConfig
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, KafkaAutoConfiguration.class, MongoAutoConfiguration.class, MultiKafkaAutoConfiguration.class})
public class Application {

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

}
