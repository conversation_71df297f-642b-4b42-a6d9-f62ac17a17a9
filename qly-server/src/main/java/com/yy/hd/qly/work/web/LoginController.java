package com.yy.hd.qly.controller;


import com.yy.java.loginvalidate.annotation.TryLogin;
import com.yy.java.loginvalidate.intercept.UserContext;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @tageeIgnore
 *  LoginController
 * <AUTHOR> gen
 */
@RestController
public class LoginController {

    /**
     * uid 接口
     * @return uid
     */
    @RequestMapping("/uid")
    @TryLogin
    public long uid() {
        return UserContext.getUid();
    }
}

