package com.yy.hd.qly.work.consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-08-04 10:07
 **/
@Component
public class ReportConsumer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @KafkaListener(containerFactory = "qlyContainerFactory", id = "qly_kafka_report_events",
            topics = "${kafka.hdzk.wzry.game.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void consume() {
        log.info("consume");
    }
}
